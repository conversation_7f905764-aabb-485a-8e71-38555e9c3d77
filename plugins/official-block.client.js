// plugins/official-block.client.js
import { defineNuxtPlugin } from '#app';

export default defineNuxtPlugin(async (nuxtApp) => {
  // 确保只在客户端执行
  if (process.client) {
    try {
      // 动态导入组件，避免SSR问题
      const { default: OfficialBlock } = await import('officialblock');

      // 使用插件方式注册（等同于Vue3中的app.use()）
      nuxtApp.vueApp.use(OfficialBlock);

      console.log('OfficialBlock 插件已成功注册');
    } catch (error) {
      console.error('OfficialBlock 插件注册失败:', error);
    }
  }
});