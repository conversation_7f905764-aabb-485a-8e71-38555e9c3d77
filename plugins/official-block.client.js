// plugins/official-block.client.js
import { defineNuxtPlugin } from '#app';

export default defineNuxtPlugin(async (nuxtApp) => {
  // 确保只在客户端执行
  if (process.client) {
    try {
      // 动态导入组件，避免SSR问题
      const { default: OfficialBlock } = await import('officialblock');
      
      // 全局注册组件
      nuxtApp.vueApp.component('OfficialBlock', OfficialBlock);
      
      console.log('OfficialBlock 组件已成功注册');
    } catch (error) {
      console.error('OfficialBlock 组件注册失败:', error);
    }
  }
});