// plugins/official-block.client.js
import { defineNuxtPlugin } from '#app';

export default defineNuxtPlugin(async (nuxtApp) => {
  try {
    console.log('开始注册插件...');

    // 首先导入并注册 Arco Design 组件
    const ArcoVue = await import('@arco-design/web-vue');
    console.log('Arco Design 组件库已导入');

    // 注册 Arco Design 组件
    nuxtApp.vueApp.use(ArcoVue.default);
    console.log('Arco Design 组件已注册');

    // 然后导入并注册 OfficialBlock 组件
    const { default: OfficialBlock } = await import('officialblock');

    // 使用插件方式注册（等同于Vue3中的app.use()）
    nuxtApp.vueApp.use(OfficialBlock);

    console.log('OfficialBlock 插件已成功注册');
    console.log('可用的全局组件:', Object.keys(nuxtApp.vueApp._context.components || {}));
  } catch (error) {
    console.error('插件注册失败:', error);
    console.error('错误详情:', error.stack);
  }
});