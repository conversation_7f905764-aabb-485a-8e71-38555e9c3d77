// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  modules: ['dayjs-nuxt'],
  // 'arco-design-nuxt-module', 
  
  // 插件配置
  plugins: [
    '~/plugins/official-block.client.js'
  ],
  css: [
    '@/assets/css/variables.css',
    '@arco-design/web-vue/dist/arco.css',
    'officialblock/style.css'
  ],
  // 构建配置
  build: {
    transpile: ['officialblock']
  },
  
  ssr: true,

  // SSR性能优化配置
  nitro: {
    // 启用压缩
    compressPublicAssets: {
      gzip: true,
      brotli: true
    },
    
  
   
  },
  
  // 渲染优化
  render: {
    // 关键CSS提取
    resourceHints: true,
    // 压缩HTML
    compressor: {
      threshold: 0
    }
  },
  

  
  // 应用级配置
  app: {
    // 页面过渡效果
    pageTransition: { name: 'page', mode: 'out-in' },
 
  },
  
  
  
  // 实验性功能
  experimental: {
    // 使用Vite作为构建工具
    vite: true,
    // 使用Nitro作为服务器引擎
    nitro: true,
    // 使用Vue 3的新特性
    reactivityTransform: true
  },
   vite: {
    css: {
      preprocessorOptions: {
        scss: {
          // additionalData: '@use "@/assets/scss/_vars.scss" as *;',
        },
      },
    },
    plugins: [
      require('vite-plugin-devtools-json').default()
    ],
    // 优化外部依赖
    optimizeDeps: {
      include: ['officialblock']
    },
    // 解决SSR环境下的全局对象问题
    define: {
      global: 'globalThis',
    },
    // 构建配置
    build: {
      rollupOptions: {
        external: [],
        output: {
          globals: {}
        }
      }
    }
  },
})