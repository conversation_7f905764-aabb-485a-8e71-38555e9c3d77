<template>
  <div class="container-content-max">
    <a-layout>
      <a-layout-header class="header">
        <headerdefault />
      </a-layout-header>
      <a-layout-content class="content">
        <slot />
      </a-layout-content>
      <a-layout-footer class="footer">
        <footerdefault />
      </a-layout-footer>
    </a-layout>
  </div>
</template>

<script setup>
import headerdefault from "./components/headerdefault";
import footerdefault from "./components/footerdefault.vue";
// 布局组件逻辑
import { ref } from "vue";
</script>

<style scoped lang="scss">
.container-content-max {
  // max-width: calc(var(--max-width) + 72px);
}

.content {
  padding: 24px;
  // background-color: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.footer {
  background-color: #fff;
}

:deep(.arco-select-dropdown) {
  z-index: 1000; /* 提高z-index确保下拉菜单不被覆盖 */
}
.header {
  width: 100%;
  display: flex;
  align-items: center;
  background-color: #fff;
  // padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
