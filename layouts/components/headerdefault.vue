<template>
  <div class="header_box">
    <!-- left -->
    <div class="logo">
      <img
        class="school_img"
        src="https://osswebsite-test.cedim.cn/media-library/shared/logo/YWIES-PLogo-Vertical-RGB-Colour-Sep2024.png"
        alt=""
      />
      <div>
        <div class="school_name">耀华国际教育学校</div>
        <div style="width: 140px">
          <a-select
            placeholder="找寻学校"
            :bordered="false"
            v-model="selectValue"
            @click="handleClick"
            @dropdown-visible-change="handleVisibleChange"
          >
            <a-option>Beijing</a-option>
            <a-option>Shanghai</a-option>
            <a-option>Guangzhou</a-option>
          </a-select>
        </div>
      </div>
    </div>
    <!-- right PC-->
    <a-row>
      <a-col :xs="0" :sm="0" :md="24" :lg="24" :xl="24" :xxl="24">
        <div class="right_box">
          <div class="right_item_top">
            <div class="school_name">浙江桐乡市耀华学校</div>
            <div class="top_list_box">
              <div class="list_detail" v-for="item in linkArr" :key="item.name">
                {{ item.name }}
              </div>
              <!-- 招生部电话 -->
              <a
                href="tel:(0573) 8896 6605"
                target="_blank"
                rel="noopener noreferrer"
                class="link-cta"
              >
                <span>招生部</span>
                <span>(0573) 8896 6605</span>
              </a>
            </div>
            <!--  -->
          </div>
          <div class="right_item_bottom">
            <div class="bottom_list_box">
              <div
                class="list_detail"
                v-for="item in linkList"
                :key="item.name"
              >
                {{ item.name }}
              </div>
              <!-- icon -->
              <div class="icon_box">
                <IconSearch style="width: 24px; height: 24px" />
                <IconUser
                  style="
                    width: 24px;
                    height: 24px;
                    margin-left: 20px;
                    margin-right: 20px;
                  "
                />
                <IconLanguage style="width: 24px; height: 24px" />
              </div>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>
    <!--  right moblie -->
    <div class="right_moblie_box">
      <div class="right_mobile_school_name">
        {{ "浙江桐乡市" }}
        <br />
        {{ "耀华学校" }}
      </div>
      <div class="icon_box">
        <IconUser style="width: 24px; height: 24px" />
        <IconLanguage
          style="
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 20px;
          "
        />
        <icon-menu style="width: 24px; height: 24px" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "YcywFrontWebHeaderdefault",

  data() {
    return {
      linkList: [
        {
          name: "关于我们",
          path: "/",
        },
        {
          name: "学术课程",
          path: "/",
        },
        {
          name: "招生部",
          path: "/",
        },
        {
          name: "校园生活",
          path: "/",
        },
        {
          name: "我们的社群",
          path: "/",
        },
        {
          name: "新闻",
          path: "/",
        },
      ],
      linkArr: [
        {
          name: "未来学校",
          path: "/",
        },
        {
          name: "梦想职业导航",
          path: "/demo",
        },
        {
          name: "申请程序",
          path: "/api-demo",
        },
        {
          name: "招生咨询",
          path: "/api-demo",
        },
        {
          name: "VR校园导览",
          path: "/contact",
        },
        {
          name: "耀中耀华网络",
          path: "/contact",
        },
      ],
      selectValue: "",
    };
  },

  mounted() {},

  methods: {
    // 添加事件处理函数
    handleClick(e) {
      console.log("Select clicked", e);
    },

    handleVisibleChange(visible) {
      console.log("Dropdown visible changed", visible);
    },
  },
};
</script>
<style lang="scss" scoped>
/* 或使用 :deep() 穿透作用域 */
:deep(.arco-select-dropdown) {
  z-index: 2000 !important;
}
.right_item_bottom {
  display: flex;
  justify-content: flex-end;
}
.bottom_list_box {
  display: flex;
  border: 1px solid red;
  align-items: center;
  .icon_box {
    margin-left: 29px;
  }
  .list_detail {
    padding: 9px 12px;
    &:hover {
      color: #64788f;
      cursor: pointer;
    }
  }
}
.right_box {
  border: 1px solid green;
  @media (max-width: 768px) {
    display: none;
  }
  .right_item_top {
    display: flex;
    .school_name {
      color: #0032a0 !important ;
      margin-right: 8px !important;
      padding: 9px 0px;
    }
    .top_list_box {
      display: flex;
      background: #f7f7fa;
      .list_detail {
        padding: 9px 12px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.428;
        text-decoration: none;
        color: #64788f;
        &:hover {
          color: #0032a0;
          cursor: pointer;
        }
      }
    }
  }
}
.link-cta {
  display: flex;
  align-items: center;
  text-decoration: none;
  cursor: pointer;
  padding: 2px 8px;
  background-color: #fff;
  box-shadow: 0 2px 6px 0 rgba(130, 130, 140, 0.12);
  border-radius: 4px;
}
.school_name {
  font-weight: 400;
  font-size: 16px;
  color: #001e60;
  text-transform: uppercase;
  text-decoration: none;
  transition: font-size 0.3s ease-in-out;
  @media (max-width: 768px) {
    font-size: 13px;
    font-weight: 400;
    color: #001e60;
    text-transform: uppercase;
    text-decoration: none;
    transition: font-size 0.3s ease-in-out;
  }
  // @media() {

  // }
}

.layout-container {
  min-height: 100vh;
}

.header_box {
  padding-left: 24px;
  padding-right: 16px;
  display: flex;
  width: 100vw;
  justify-content: space-between;
  @media (max-width: 768px) {
    padding-left: 12px;
    padding-right: 12px;
    height: 72px;
  }

  @media (max-width: 480px) {
    padding-left: 12px;
    padding-right: 12px;
    height: 72px;
  }
}
.logo {
  padding: 13px 0;
  font-size: 18px;
  font-weight: bold;
  color: #165dff;
  display: flex;
}
.school_img {
  width: auto;
  height: 48px;
  margin-right: 16px;
  @media (max-width: 768px) {
    height: 30px;
    margin-right: 12px;
  }
}

.right_moblie_box {
  display: none;
  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    height: 72px;
    .right_mobile_school_name {
      margin-right: 8px !important;
      color: var(--text-color-primary);
    }
  }
}
</style>
