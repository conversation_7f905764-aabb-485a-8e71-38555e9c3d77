<template>
  <div class="footer_wrap_total">
    <!-- pc -->
    <div class="footer_wrap">
      <!-- left -->
      <div class="footer_left">
        <!-- 共创人类美好 -->
        <div class="left_top_box">
          <a
            class="footer-logo__logo"
            href="https://www.ywies.cn/"
            target="_blank"
          >
            <img src="@/assets/images/joint-ywies-ycyw.svg" alt="" />
          </a>
          <div class="logo_text">共創人类美好未来</div>
        </div>
        <!-- 左侧导航 -->
        <div class="left_link_wrap">
          <div class="link_detail" v-for="item in bottomLink" :key="item.title">
            <div class="link_title">{{ item.title }}</div>
            <a
              :href="child.name"
              class="link_list"
              v-for="child in item.children"
              :key="child.name"
              >{{ child.name }}</a
            >
          </div>
        </div>
      </div>
      <!-- right -->
      <div class="footer_right">
        <a-row class="wx_wrap">
          <!-- -->
          <a-col>
            <div class="right_top_wx">
              <img
                class="qcode_img"
                src="@/assets/images/ywies-tx-wechat-qrcode.jpg"
                alt=""
              />
              <div class="qcode_right">
                <img
                  class="wx_icon"
                  src="@/assets/images/icon-wechat-full-sc.png"
                  alt=""
                />
                <div class="fllow_us">关注我们的额微信公众号获取最新消息</div>
              </div>
            </div>
            <div class="footer_contact">
              <div class="school_name">耀华国际教育学校浙江桐乡校区</div>
              <a
                href="https://map.baidu.com/poi/耀华国际教育学校(浙江桐乡校区)/@13422013.625963753,3551835.6050974904,16.95z?uid=248fa2a7a83ed6aac9e33c5b&amp;querytype=detailConInfo"
                target="_blank"
                rel="noopener noreferrer"
                class="contact_info"
                ><span
                  >中国浙江桐乡市高桥镇合悦大道88号（邮编： 314515）</span
                ></a
              >
              <a href="tel:(0573) 8896 6605" class="contact_info"
                ><span
                  ><div class="phone-prefix__one-number --mobile-hidden">
                    招生部热线: (0573) 8896 6605
                  </div>
                  <div class="d-inline-block text-nowrap"></div></span
              ></a>
              <a href="mailto:<EMAIL>" class="contact_info"
                ><span>电邮: <EMAIL></span></a
              >
            </div>
          </a-col></a-row
        >
      </div>
    </div>
    <div class="footer_campus_bottom">
      <div class="footer_campus_bottom_wrap">
        <div class="school_intrduce">
          耀华国际教育学校浙江桐乡校区是一所确保儿童安全的学校
        </div>
        <div class="school_intrduce_click">
          <span>点击</span>
          <a class="go_children" href="/sc/about-y" target="_self">此处</a>
          <span>了解我们的《儿童保护规章和守则》</span>
        </div>
        <div class="copyright_wrap">
          <div>
            <div class="copyright_bottom_box">
              <div
                class="right_link"
                v-for="(item, index) in copyrightList"
                :key="index"
                :class="[
                  index === copyrightList.length - 1 ? 'border_none' : '',
                ]"
              >
                <a class="link_text" href="/sc/axa">{{ item.name }}</a>
              </div>
            </div>
            <!-- 版权 -->
            <p class="copyright_text">© 2022 浙江桐乡市耀华学校版权所有</p>
          </div>
          <!-- 右侧的图片 -->
          <a
            class="cambridge_img"
            href="https://www.cambridgeinternational.org/"
            target="_blank"
          >
            <img
              src="https://osswebsite-test.cedim.cn/shared/accreds/cambridge-assessment-international-education.png"
              alt=""
            />
          </a>
        </div>
      </div>
    </div>
    <!-- mobile -->
    <div class="mobile_wrap">
      <div class="logo_text">共創人类美好未来</div>
      <a class="footer-logo__logo" href="https://www.ywies.cn/" target="_blank">
        <img src="@/assets/images/joint-ywies-ycyw.svg" alt="" />
      </a>
      <div class="mobile_school_name">耀华国际教育学校浙江桐乡校区</div>
      <div>
        <a
          href="https://map.baidu.com/poi/耀华国际教育学校(浙江桐乡校区)/@13422013.625963753,3551835.6050974904,16.95z?uid=248fa2a7a83ed6aac9e33c5b&amp;querytype=detailConInfo"
          target="_blank"
          rel="noopener noreferrer"
          class="contact_info_mobile"
          ><span>中国浙江桐乡市高桥镇合悦大道88号（邮编： 314515）</span></a
        >
        <a href="tel:(0573) 8896 6605" class="contact_info_mobile"
          ><span
            ><div class="phone-prefix__one-number --mobile-hidden">
              招生部热线: (0573) 8896 6605
            </div>
            <div class="d-inline-block text-nowrap"></div></span
        ></a>
        <a href="mailto:<EMAIL>" class="contact_info_mobile"
          ><span>电邮: <EMAIL></span></a
        >
      </div>
      <!-- 微信 -->
      <div class="right_top_wx">
        <img
          class="qcode_img"
          src="@/assets/images/ywies-tx-wechat-qrcode.jpg"
          alt=""
        />
        <div class="qcode_right">
          <img
            class="wx_icon"
            src="@/assets/images/icon-wechat-full-sc.png"
            alt=""
          />
          <div class="fllow_us">关注我们的额微信公众号获取最新消息</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "YcywFrontWebFooter",
  data() {
    return {
      bottomLink: [
        {
          title: "关于我们",
          children: [
            {
              name: "概要",
            },
            {
              name: "创办人寄语",
            },
            {
              name: "使命、理念与实践、校训",
            },
            {
              name: "耀华教育优势",
            },
            {
              name: "我们的独特之处",
            },
            {
              name: "儿童保护",
            },
            {
              name: "楚珩日2024",
            },
            {
              name: "耀中耀华90周年",
            },
          ],
        },
        {
          title: "学术课程",
          children: [
            {
              name: "幼儿教育课程",
            },
            {
              name: "小学课程",
            },
            {
              name: "初中课程",
            },
            {
              name: "高中课程(未来学校)",
            },
            {
              name: "职业及升学辅导办公室",
            },
            {
              name: "英国萨默塞特耀华教室",
            },
            {
              name: "世界教室",
            },
            {
              name: "英语水平提升",
            },
            {
              name: "艺术与音乐",
            },
            {
              name: "学习共同体",
            },
          ],
        },
        {
          title: "招生部",
          children: [
            {
              name: "申请程序",
            },
            {
              name: "常见问题",
            },
            {
              name: "奖学金",
            },
          ],
        },
        {
          title: "校园生活",
          children: [
            {
              name: "校历",
            },
            {
              name: "校园及设施",
            },
            {
              name: "联课活动",
            },
            {
              name: "校园服务",
            },
            {
              name: "幸福关怀",
            },
            {
              name: "短途学习游",
            },
            {
              name: "学生宿舍",
            },
          ],
        },
        {
          title: "我们的社群",
          children: [
            {
              name: "耀中耀华网络",
            },
            {
              name: "我们的团队",
            },
            {
              name: "我们的学生",
            },
            {
              name: "我们的家长",
            },
            {
              name: "加入团队",
            },
            {
              name: "支持我们",
            },
            {
              name: "耀中耀华纪念商品",
            },
          ],
        },
        {
          title: "新闻",
          children: [{ name: "新闻" }, { name: "媒体" }],
        },
      ],
      copyrightList: [
        {
          name: "网站地图",
          path: "/dwdw",
        },
        {
          name: "法律信息",
          path: "/dwdw",
        },
        {
          name: "隐私政策",
          path: "/dwdw",
        },
        {
          name: "浙ICP备20222222号",
          path: "/dwdw",
        },
        {
          name: "浙公网安备22312312号",
          path: "/dwdw",
        },
      ],
    };
  },

  mounted() {},

  methods: {},
};
</script>
<style lang="scss" scoped>
.mobile_wrap {
  display: none;
  @media (max-width: 768px) {
    display: block;
    text-align: center;
    .logo_text {
      font-size: 16px;
      color: #1a1c20;
      font-size: 16px;
      margin-bottom: 28px;
      font-weight: bold;
    }
    .mobile_school_name {
      color: var(--text-color-primary);
      font-size: 16px;
      font-weight: bold;
      margin-top: 15px;
      margin-bottom: 20px;
    }
    .contact_info_mobile {
      color: var(--text-color-secondary);
      font-size: 13px;
      font-weight: 300;
      user-select: none;
      background-color: transparent;
      cursor: pointer;
      position: relative;
      text-decoration: none;
      margin-bottom: 9px;
      display: inline-block;
    }
    .right_top_wx {
      display: flex;
      padding: 12px;
      background: #f7f7fa;
      border-radius: 2px;
      margin-bottom: 44px;
      margin-top: 44px;
      .qcode_right {
        // width: 100%;
      }
      .qcode_img {
        width: 88px;
        margin-right: 16px;
      }
      .fllow_us {
        font-size: 14px;
        font-weight: 300;
        width: 230px;
      }
      .wx_icon {
        height: 24px;
        margin-bottom: 8px;
      }
    }
  }
}
.footer_wrap_total {
}
.footer_wrap {
  display: flex;
  justify-content: space-between;
  margin-bottom: 48px;
  padding-left: 36px;
  padding-right: 36px;
  margin: 0 auto;
  max-width: calc(var(--max-width) + 72px);
  border: 1px solid blue;
  @media (max-width: 768px) {
    display: none;
  }
  .footer_left {
    border: 1px solid red;
    .left_top_box {
      display: flex;
      align-items: center;
      margin-bottom: 85px;
      .footer-logo__logo {
        height: 82.5px;
        margin-right: 32px;
      }

      .logo_text {
        color: #1a1c20;
        font-size: 18px;
        font-weight: 400;
        max-width: 190px;
      }
    }
    .left_link_wrap {
      display: flex;
      justify-content: space-between;
      .link_detail {
        margin-right: 32px;
        flex: 1 1 0;
        .link_title {
          text-decoration: none;
          background-image: linear-gradient(
            90deg,
            var(--text-color, #0048e8),
            var(--primary-color, #0066ff)
          );
          background-size: 0 1px, 100% 1px;
          background-position: 0 100%, 100% 100%;
          background-repeat: no-repeat;
          transition: background-size 0.3s;
          padding-bottom: 2px;
        }
        .link_list {
          display: block;
          font-size: var(--font-size-small);
          font-weight: 400;
          line-height: var(--line-height-normal);
          color: var(--text-color-secondary);
        }
      }
    }
  }
  .footer_right {
    border: 1px solid green;
    .right_top_wx {
      display: flex;
      padding: 12px;
      background: #f7f7fa;
      border-radius: 2px;
      margin-bottom: 44px;
      .qcode_right {
        // width: 100%;
      }
      .qcode_img {
        width: 88px;
        margin-right: 16px;
      }
      .fllow_us {
        font-size: 14px;
        font-weight: 300;
        width: 230px;
      }
      .wx_icon {
        height: 24px;
        margin-bottom: 8px;
      }
    }
    .footer_contact {
      border: 1px solid pink;
      .school_name {
        color: var(--text-color-primary);
        font-size: 18px;
        font-weight: 600;
        line-height: 24px;
      }
      .contact_info {
        line-height: 20px;
        font-size: 14px;
        font-weight: 300;
        color: var(--text-color);
      }
    }
  }
}
.footer_campus_bottom {
  background: url("../../assets/images/bg.svg");
  background-position: top;
  background-size: 100% auto;
  background-repeat: no-repeat;
  padding-top: 44.5px;
  padding-bottom: 34px;
  padding-left: 40px;
  padding-right: 40px;
  .footer_campus_bottom_wrap {
    padding-left: 36px;
    padding-right: 36px;
    margin: 0 auto;
    max-width: calc(var(--max-width) + 72px);
  }
  .school_intrduce {
    color: #fff;
    font-size: 14px;
    font-weight: 300;
    line-height: 20px;
  }
  .school_intrduce_click {
    color: #fff;
    font-size: 14px;
    font-weight: 300;
    line-height: 20px;
    .go_children {
      color: #fff;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }
  }
  .copyright_wrap {
    display: flex;
    justify-content: space-between;
    .cambridge_img {
      display: inline-block;
      height: 40px;
      width: auto;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .copyright_bottom_box {
    display: flex;
    margin-left: -24px;
    margin-top: 27px;
    .right_link {
      padding-right: 24px;
      padding-left: 24px;
      font-size: 14px;
      font-weight: 300;
      line-height: 1.214;
      color: #fff;
      border-right: 1px solid #fff;
      .link_text {
        color: #fff;
        white-space: nowrap;
        text-decoration: none;
      }
    }
  }
  .copyright_text {
    margin-top: 8px;
    margin-right: 24px;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.214;
    color: #fff;
  }
  @media (max-width: 768px) {
    display: none;
  }
}
.border_none {
  border: none !important;
}
</style>
