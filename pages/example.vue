<template>
  <div class="example-page">
    <h1>OfficialBlock 组件示例</h1>
    
    <!-- 方式1: 使用全局注册的组件 -->
    <div class="example-section">
      <h2>方式1: 全局组件使用</h2>
      <OfficialBlock 
        v-if="mounted"
        :prop1="exampleProp1"
        :prop2="exampleProp2"
        @custom-event="handleCustomEvent"
      />
    </div>
    
    <!-- 方式2: 使用ClientOnly包装 -->
    <div class="example-section">
      <h2>方式2: ClientOnly包装使用</h2>
      <ClientOnly>
        <OfficialBlock 
          :prop1="exampleProp1"
          :prop2="exampleProp2"
          @custom-event="handleCustomEvent"
        />
        <template #fallback>
          <div class="loading">组件加载中...</div>
        </template>
      </ClientOnly>
    </div>
    
    <!-- 方式3: 动态导入使用 -->
    <div class="example-section">
      <h2>方式3: 动态导入使用</h2>
      <component 
        v-if="DynamicOfficialBlock && mounted"
        :is="DynamicOfficialBlock"
        :prop1="exampleProp1"
        :prop2="exampleProp2"
        @custom-event="handleCustomEvent"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 页面元数据
definePageMeta({
  title: 'OfficialBlock 组件示例'
});

// 响应式数据
const mounted = ref(false);
const DynamicOfficialBlock = ref(null);
const exampleProp1 = ref('示例属性1');
const exampleProp2 = ref('示例属性2');

// 组件挂载后执行
onMounted(async () => {
  mounted.value = true;
  
  // 动态导入组件（方式3）
  try {
    const { default: OfficialBlock } = await import('OfficialBlock');
    DynamicOfficialBlock.value = OfficialBlock;
  } catch (error) {
    console.error('动态导入OfficialBlock失败:', error);
  }
});

// 事件处理
const handleCustomEvent = (data: any) => {
  console.log('接收到自定义事件:', data);
};
</script>

<style scoped>
.example-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.example-section h2 {
  margin-top: 0;
  color: #333;
}

.loading {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>