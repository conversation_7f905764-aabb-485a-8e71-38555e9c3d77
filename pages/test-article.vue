<template>
  <div class="test-container">
    <h1>ArticleList 组件测试页面</h1>
    
    <div class="test-section">
      <h2>测试1: 基本使用</h2>
      <ClientOnly>
        <ArticleList v-model="articleData" />
        <template #fallback>
          <div class="loading">ArticleList 组件加载中...</div>
        </template>
      </ClientOnly>
    </div>

    <div class="test-section">
      <h2>测试2: 检查数据</h2>
      <pre>{{ JSON.stringify(articleData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
const articleData = ref({
  id: 'test-article-1',
  type: 'ArticleList',
  isPreview: false,
  width: '',
  background: 'white',
  data: [
    {
      id: 'article-1',
      type: 'Article',
      data: {
        title: '测试文章标题',
        content: '这是一个测试文章的内容，用来验证ArticleList组件是否能正常渲染。',
        buttonList: [
          {
            id: 'btn-1',
            text: '了解更多',
            url: 'https://www.example.com',
            isExternal: true
          }
        ],
        linkList: [
          {
            id: 'link-1',
            text: '相关链接',
            url: 'https://www.example.com',
            isExternal: true
          }
        ]
      }
    },
    {
      id: 'image-1',
      type: 'Image',
      data: {
        imgSrc: 'https://osswebsite.ycyw.com/media-library/ywies-bj/images/home/<USER>',
        caption: '测试图片',
        alt: '测试图片描述',
        isRound: false
      }
    }
  ]
});

// 监听数据变化
watch(articleData, (newValue) => {
  console.log('ArticleList 数据变化:', newValue);
}, { deep: true });
</script>

<style scoped>
.test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

h1 {
  color: #333;
  margin-bottom: 30px;
}

h2 {
  color: #666;
  margin-bottom: 20px;
}
</style>
