<template>
  <div class="home-container">
    首页
    <ClientOnly>
      <ArticleList
        v-model="articleValue"
        size="medium"
        :disabled="false"
        @change="handleChange"
      >
        <template #header="{ title }">
          <h3>{{ title }}</h3>
        </template>
        <template #default="{ value }">
          <p>当前值: {{ value }}</p>
        </template>
      </ArticleList>
      <template #fallback>
        <div>加载中...</div>
      </template>
    </ClientOnly>
  </div>
</template>

<script setup>
import { ArticleList } from "Officialblock";
const numDemo = ref(0);
// 页面元数据
// definePageMeta({
//   layout: "default",
// });
const articleValue = ref({
  title: "Hello World",
  content: "这是文章内容",
  author: "作者",
  date: new Date().toISOString(),
});

const handleChange = (value) => {
  console.log("值改变了:", value);
};
</script>

<style scoped>
.home-container {
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 64px - 70px - 48px);
}

.welcome-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}
</style>
