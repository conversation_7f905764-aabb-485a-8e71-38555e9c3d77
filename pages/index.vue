<template>
  <div class="home-container">
    首页
    <ClientOnly>
      <ArticleList
        v-model="articleValue"
      >
      </ArticleList>
      <template #fallback>
        <div>加载中...</div>
      </template>
    </ClientOnly>
  </div>
</template>

<script setup>
import { ArticleList } from "Officialblock";
import 'officialblock/style.css'
const numDemo = ref(0);
// 页面元数据
// definePageMeta({
//   layout: "default",
// });
const articleValue = ref({});

const handleChange = (value) => {
  console.log("值改变了:", value);
};
</script>

<style scoped>
.home-container {
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 64px - 70px - 48px);
}

.welcome-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}
</style>
