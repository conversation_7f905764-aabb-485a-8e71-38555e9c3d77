<template>
  <div class="debug-container">
    <h1>调试页面</h1>
    
    <div class="debug-section">
      <h2>1. 检查 Arco Design 组件</h2>
      <a-button type="primary">Arco Design 按钮测试</a-button>
      <a-select placeholder="选择一个选项" style="width: 200px; margin-left: 10px;">
        <a-option value="1">选项1</a-option>
        <a-option value="2">选项2</a-option>
      </a-select>
    </div>

    <div class="debug-section">
      <h2>2. 检查 OfficialBlock 插件状态</h2>
      <p>插件加载状态: {{ pluginStatus }}</p>
      <p>可用组件: {{ availableComponents }}</p>
    </div>

    <div class="debug-section">
      <h2>3. 测试 ArticleList 组件</h2>
      <ClientOnly>
        <div v-if="showArticleList">
          <ArticleList v-model="testData" />
        </div>
        <div v-else>
          <button @click="loadArticleList" class="load-btn">加载 ArticleList 组件</button>
        </div>
        <template #fallback>
          <div class="loading">组件加载中...</div>
        </template>
      </ClientOnly>
    </div>

    <div class="debug-section">
      <h2>4. 控制台日志</h2>
      <p>请打开浏览器开发者工具查看控制台输出</p>
    </div>
  </div>
</template>

<script setup>
const showArticleList = ref(false);
const pluginStatus = ref('检查中...');
const availableComponents = ref([]);

const testData = ref({
  id: 'debug-test',
  type: 'ArticleList',
  isPreview: false,
  background: 'white',
  data: [
    {
      id: 'debug-article',
      type: 'Article',
      data: {
        title: '调试测试文章',
        content: '这是用于调试的测试文章内容。',
        buttonList: [],
        linkList: []
      }
    }
  ]
});

const loadArticleList = () => {
  console.log('尝试加载 ArticleList 组件...');
  showArticleList.value = true;
};

onMounted(() => {
  console.log('调试页面已挂载');
  
  // 检查插件状态
  const app = getCurrentInstance()?.appContext.app;
  if (app) {
    console.log('Vue 应用实例:', app);
    console.log('全局组件:', Object.keys(app._context.components || {}));
    
    pluginStatus.value = '已获取应用实例';
    availableComponents.value = Object.keys(app._context.components || {});
  } else {
    pluginStatus.value = '无法获取应用实例';
  }
  
  // 检查 Arco Design
  console.log('检查 Arco Design 组件...');
  
  // 延迟加载 ArticleList
  setTimeout(() => {
    console.log('自动加载 ArticleList 组件');
    showArticleList.value = true;
  }, 2000);
});
</script>

<style scoped>
.debug-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.debug-section h2 {
  color: #333;
  margin-bottom: 15px;
}

.load-btn {
  background: #0066ff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.load-btn:hover {
  background: #0052cc;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

h1 {
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}
</style>
